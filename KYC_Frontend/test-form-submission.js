// Test script to verify form submission payload format
// This script tests the mapFormDataToPayload function

// Mock data that matches the form structure
const mockPersonalInfo = {
  name: "<PERSON>",
  date_of_birth: "25/06/1990", // DD/MM/YYYY format from form
  gender: "Male",
  father_name: "<PERSON>",
  mother_name: "<PERSON>",
  martial_status: true,
  spouse_name: "<PERSON>",
  no_of_dependent: 2,
  name_of_guardian: "<PERSON>",
  relation_with_gurdian: "Father"
};

const mockAdditionalInfo = {
  nationality: "Indian",
  country_name: "India",
  citizenship: "Indian",
  religion: "Hindu",
  persion_with_disability: false,
  education: "Graduate",
  occupation: "Software Engineer",
  organisation_name: "Tech Corp",
  designation_profession: "Senior Developer",
  nature_of_bussiness: "IT Services",
  net_worth: "1000000"
};

// Expected API payload format
const expectedPayload = {
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "<PERSON>",
  "date_of_birth": "1990-06-25", // Should be converted to YYYY-MM-DD
  "gender": "Male",
  "father_name": "<PERSON>",
  "mother_name": "<PERSON>e",
  "martial_status": true,
  "spouse_name": "<PERSON> Doe",
  "no_of_dependent": 2,
  "name_of_guardian": "Robert Doe",
  "relation_with_gurdian": "Father",
  "nationality": "Indian",
  "country_name": "India",
  "citizenship": "Indian",
  "religion": "Hindu",
  "persion_with_disability": false,
  "education": "Graduate",
  "occupation": "Software Engineer",
  "organisation_name": "Tech Corp",
  "designation_profession": "Senior Developer",
  "nature_of_bussiness": "IT Services",
  "net_worth": 1000000, // Should be converted to number
  "DateOfCreation": "2025-06-26T10:51:23.425Z",
  "ModifiedDate": "2025-06-26T10:51:23.425Z"
};

console.log("Mock Personal Info:", JSON.stringify(mockPersonalInfo, null, 2));
console.log("Mock Additional Info:", JSON.stringify(mockAdditionalInfo, null, 2));
console.log("Expected API Payload:", JSON.stringify(expectedPayload, null, 2));

// Test the date conversion function
function convertDateFormat(dateString) {
  if (!dateString || !dateString.includes("/")) {
    return dateString;
  }

  const [day, month, year] = dateString.split("/");
  if (
    day &&
    month &&
    year &&
    day.length === 2 &&
    month.length === 2 &&
    year.length === 4
  ) {
    return `${year}-${month}-${day}`;
  }

  return dateString;
}

// Test date conversion
console.log("Date conversion test:");
console.log("Input: 25/06/1990");
console.log("Output:", convertDateFormat("25/06/1990"));
console.log("Expected: 1990-06-25");

// Test net_worth conversion
console.log("Net worth conversion test:");
console.log("Input: '1000000'");
console.log("Output:", parseInt("1000000") || 0);
console.log("Expected: 1000000");
