import React, { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  selectCurrentApplicationId,
  switchToApplication,
  deleteApplication,
  validateAndFixProgress,
  selectDashboardRefreshTrigger,
} from "@/redux/slices/kycSlice";
import { useUnifiedKycData } from "@/hooks/useUnifiedKycData";
import { useProgressValidation } from "@/hooks/useProgressValidation";
import { Button } from "@/components/ui/button";
import {
  CircleFadingPlus,
  CircleCheck,
  RefreshCw,
  AlertCircle,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import {
  getStatusColor,
  getStatusText,
  getNextIncompleteStep,
  getResumeRoute,
  validateProgressConsistency,
  DEFAULT_DASHBOARD_CONFIG,
} from "@/types/unifiedDashboard";
import { kycService } from "@/services/kycService";


const UnifiedKycDashboard = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);
  const dashboardRefreshTrigger = useAppSelector(selectDashboardRefreshTrigger);

  // Add these lines for user_id, page_no, and page_length
  const user = useAppSelector((state) => state.auth.user);
  const user_id = user?.user_id;
  const page_no = 1;
  const page_length = 10;

  // Local state for onboarding users
  const [onboardingUsers, setOnboardingUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // Function to fetch onboarding users
  const fetchOnboardingUsers = async () => {
    if (!user_id) return;

    setLoading(true);
    try {
      const response = await kycService.getAllOnboardingUsers(
        user_id,
        page_no,
        page_length
      );
      console.log("Onboarding users response:", response);
      if (response && Array.isArray(response)) {
        // Debug: Log the structure of onboarding users
        if (response.length > 0) {
          console.log("First onboarding user structure:", response[0]);
          const firstUser = response[0] as any;
          console.log("Available ID fields:", {
            id: firstUser.id,
            Id: firstUser.Id,
            ID: firstUser.ID,
            user_id: firstUser.user_id,
            userId: firstUser.userId,
            User_id: firstUser.User_id,
          });
        }
        setOnboardingUsers(response);
      } else if (
        (response as any)?.IsSuccess &&
        (response as any)?.onboarding_user
      ) {
        const users = (response as any).onboarding_user;
        // Debug: Log the structure of onboarding users from nested response
        if (Array.isArray(users) && users.length > 0) {
          console.log("First onboarding user structure (nested):", users[0]);
          const firstUser = users[0] as any;
          console.log("Available ID fields (nested):", {
            id: firstUser.id,
            Id: firstUser.Id,
            ID: firstUser.ID,
            user_id: firstUser.user_id,
            userId: firstUser.userId,
            User_id: firstUser.User_id,
          });
        }
        setOnboardingUsers(users);
      } else {
        setOnboardingUsers([]);
      }
    } catch (error) {
      console.error("Error fetching onboarding users:", error);
      setOnboardingUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user_id) {
      fetchOnboardingUsers();
    }
  }, [user_id, page_no, page_length]);

  // Listen for dashboard refresh trigger from other components
  useEffect(() => {
    if (dashboardRefreshTrigger > 0) {
      fetchOnboardingUsers();
    }
  }, [dashboardRefreshTrigger]);

  // Unified KYC data with enhanced API integration
  const {
    applications: unifiedApplications,
    stats,
    isLoading,
    hasErrors,
    apiErrors,
    refreshData: refreshKycData,
    lastSyncTimestamp,
    sortApplications,
  } = useUnifiedKycData();

  // Combined refresh function for both KYC applications and onboarding users
  const refreshData = async () => {
    await Promise.all([refreshKycData(), fetchOnboardingUsers()]);
  };

  // Automatically validate and fix progress inconsistencies
  useProgressValidation();

  // Local state for table management
  const [sortBy, setSortBy] = useState(
    DEFAULT_DASHBOARD_CONFIG.sortBy || "updatedAt"
  );
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(
    DEFAULT_DASHBOARD_CONFIG.sortDirection || "desc"
  );

  // Sort applications using the hook's utility function
  const sortedApplications = useMemo(() => {
    return sortApplications(unifiedApplications, sortBy as any, sortDirection);
  }, [unifiedApplications, sortBy, sortDirection, sortApplications]);

  // Handle Complete Your KYC button click
  const handleCompleteKYC = () => {
    navigate("/kyc/verification-process");
  };

  // Handle Add New KYC button click
  const handleAddNewKYC = () => {
    navigate("/kyc/verification-process");
  };

  // Handle continue application with resume functionality
  const handleContinueApplication = (applicationId: string) => {
    const application = unifiedApplications.find(
      (app) => app.id === applicationId
    );
    if (!application) {
      // Check if this is an onboarding user
      const onboardingUser = onboardingUsers.find(
        (user: any) => user.id === applicationId || user.Id === applicationId
      );
      if (onboardingUser) {
        handleContinueOnboardingUser(onboardingUser);
        return;
      }
      return;
    }

    dispatch(switchToApplication(applicationId));

    // Validate progress consistency and fix if needed
    const validation = validateProgressConsistency(application);
    if (!validation.isConsistent) {
      console.warn("Progress inconsistency detected:", validation.issues);
      dispatch(validateAndFixProgress());
      toast.warning("Progress data was inconsistent and has been corrected.");
    }

    // Determine the next incomplete step for resume functionality
    const nextStep = getNextIncompleteStep(application);
    const resumeRoute = getResumeRoute(nextStep);

    // Show a toast indicating where the user will resume
    if (nextStep !== application.currentStep) {
      toast.info(`Resuming from: ${getStepName(nextStep)}`);
    }

    navigate(resumeRoute);
  };

  // Handle continue for onboarding users (redirect to OTP if not verified)
  const handleContinueOnboardingUser = (onboardingUser: any) => {
    const isEmailVerified = onboardingUser.isEmailverified || false;
    const isPhoneVerified = onboardingUser.isphoneverified || false;

    // If email is not verified, redirect to email OTP
    if (!isEmailVerified) {
      toast.info("Please verify your email first");
      navigate("/verify-otp", {
        state: {
          type: "email",
          contact: onboardingUser.email,
          redirectTo: "/dashboard",
        },
      });
      return;
    }

    // If phone is not verified, redirect to phone OTP
    if (!isPhoneVerified) {
      toast.info("Please verify your phone number");
      navigate("/verify-otp", {
        state: {
          type: "mobile",
          contact: onboardingUser.phone_no,
          redirectTo: "/dashboard",
        },
      });
      return;
    }

    // If both are verified, proceed to KYC flow
    toast.success("Contact verification completed! Starting KYC process");
    navigate("/kyc/personal");
  };

  // Helper function to extract the correct ID from onboarding user object
  const getOnboardingUserId = (user: any): string => {
    // Try different possible ID field names
    return (
      user.id ||
      user.Id ||
      user.ID ||
      user.user_id ||
      user.userId ||
      user.User_id
    );
  };

  // Handle delete onboarding user
  const handleDeleteOnboardingUser = async (userId: string) => {
    if (
      !window.confirm("Are you sure you want to delete this onboarding user?")
    ) {
      return;
    }

    // Debug: Log the user ID being passed
    console.log("Attempting to delete onboarding user with ID:", userId);

    try {
      // Call the delete API
      await kycService.deleteOnboardingUser(userId);

      // Remove from local state after successful API call
      setOnboardingUsers((prev) =>
        prev.filter((user: any) => getOnboardingUserId(user) !== userId)
      );

      toast.success("Onboarding user deleted successfully");
    } catch (error) {
      console.error("Error deleting onboarding user:", error);
      console.error("Full error details:", error);

      // More detailed error message
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(`Failed to delete onboarding user: ${errorMessage}`);

      // Refresh the list to restore the user if delete failed
      if (user_id) {
        try {
          const response = await kycService.getAllOnboardingUsers(
            user_id,
            page_no,
            page_length
          );
          if (response && Array.isArray(response)) {
            setOnboardingUsers(response);
          }
        } catch (refreshError) {
          console.error("Error refreshing onboarding users:", refreshError);
        }
      }
    }
  };

  // Helper function to get step name for toast messages
  const getStepName = (step: number): string => {
    switch (step) {
      case 1:
        return "Contact Verification";
      case 2:
        return "Personal Information";
      case 3:
        return "Additional Information";
      case 4:
        return "Address Details";
      case 5:
        return "Facial Verification";
      case 6:
        return "Document Upload";
      case 7:
        return "Verification";
      case 8:
        return "Summary";
      case 9:
        return "Review & Confirm";
      case 10:
        return "Completion";
      default:
        return "Unknown Step";
    }
  };

  // Handle view application
  const handleViewApplication = (applicationId: string) => {
    dispatch(switchToApplication(applicationId));
    navigate("/kyc/review");
  };

  // Handle delete application
  const handleDeleteApplication = (applicationId: string) => {
    if (
      window.confirm("Are you sure you want to delete this KYC application?")
    ) {
      dispatch(deleteApplication(applicationId));
      toast.success("KYC application deleted successfully");
    }
  };

  // Handle column sorting
  const handleSort = (columnKey: string) => {
    if (sortBy === columnKey) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(columnKey);
      setSortDirection("desc");
    }
  };

  return (
    <div className="w-full">
      {/* Header with API Status and Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-bold">KYC Applications</h2>
          {lastSyncTimestamp && (
            <span className="text-sm text-gray-500">
              Last synced: {new Date(lastSyncTimestamp).toLocaleTimeString()}
            </span>
          )}
        </div>
        <div className="flex items-center gap-2 mt-2 sm:mt-0">
          {unifiedApplications.length > 0 && (
            <Button
              className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-4 py-2"
              onClick={handleAddNewKYC}
            >
              <CircleFadingPlus size={16} className="mr-2" />
              Add New KYC
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          {hasErrors && (
            <span className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle size={16} />
              API sync issues
            </span>
          )}
        </div>
      </div>

      {/* Error Display
      {hasErrors && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle size={16} className="text-red-600" />
            <span className="text-red-800 font-medium">
              API Synchronization Issues
            </span>
          </div>
          <div className="text-sm text-red-700">
            {Object.entries(apiErrors).map(
              ([key, error]) =>
                error && (
                  <div key={key} className="mb-1">
                    <strong>{key}:</strong> {String(error)}
                  </div>
                )
            )}
          </div>
        </div>
      )}
 */}
      {/* Main Content */}
      {unifiedApplications.length === 0 && onboardingUsers.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex flex-col items-center">
            <div
              className="w-10 h-10 rounded-xl cursor-pointer bg-[#26355E] hover:bg-primary-navy/90 flex items-center justify-center text-white mb-4"
              onClick={handleCompleteKYC}
            >
              <CircleFadingPlus size={28} />
            </div>
            <Button
              className="bg-[#26355E] hover:bg-primary-navy/90 text-white py-6 px-12 md:px-44 rounded-lg text-lg font-medium"
              onClick={handleCompleteKYC}
            >
              Complete Your KYC
            </Button>
          </div>
        </div>
      ) : (
        <div className="overflow-x-auto rounded-lg border border-gray-200">
          <div className="min-w-[800px]">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-4 px-4 font-medium text-gray-700">
                    Email & Phone Number
                  </th>
                  <th className="text-center py-4 px-4 font-medium text-gray-700">
                    Facial Verification
                  </th>
                  <th className="text-center py-4 px-4 font-medium text-gray-700">
                    <button
                      onClick={() => handleSort("status")}
                      className="flex items-center gap-1 hover:text-gray-900 mx-auto"
                    >
                      KYC Status
                      {sortBy === "status" &&
                        (sortDirection === "asc" ? (
                          <ChevronUp size={16} />
                        ) : (
                          <ChevronDown size={16} />
                        ))}
                    </button>
                  </th>
                  <th className="text-center py-4 px-4 font-medium text-gray-700">
                    <button
                      onClick={() => handleSort("lastUpdated")}
                      className="flex items-center gap-1 hover:text-gray-900 mx-auto"
                    >
                      Submission Date
                      {sortBy === "lastUpdated" &&
                        (sortDirection === "asc" ? (
                          <ChevronUp size={16} />
                        ) : (
                          <ChevronDown size={16} />
                        ))}
                    </button>
                  </th>
                  <th className="text-center py-4 px-4 font-medium text-gray-700">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {sortedApplications.map((application: any) => (
                  <React.Fragment key={application.id}>
                    {/* Main Row */}
                    <tr
                      className={`border-b hover:bg-gray-50 ${
                        currentApplicationId === application.id
                          ? "bg-blue-50"
                          : ""
                      }`}
                    >
                      {/* Email & Phone Number */}
                      <td className="py-4 px-4">
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-900">
                              Email:
                            </span>
                            <span className="text-sm text-gray-600">
                              {application.email}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-900">
                              Phone:
                            </span>
                            <span className="text-sm text-gray-600">
                              {application.phone}
                            </span>
                          </div>
                        </div>
                      </td>

                      {/* Facial Verification */}
                      <td className="py-4 px-4 text-center">
                        <span
                          className={`px-3 py-1 rounded-full text-sm border ${
                            application.verificationStatus?.facial?.verified
                              ? "bg-green-50 text-green-700 border-green-200"
                              : "bg-gray-50 text-gray-700 border-gray-200"
                          }`}
                        >
                          {application.verificationStatus?.facial?.verified
                            ? "Validated"
                            : "Not Validated"}
                        </span>
                      </td>

                      {/* KYC Status */}
                      <td className="py-4 px-4 text-center">
                        <span
                          className={`px-3 py-1 rounded-full text-sm border ${getStatusColor(
                            application.status
                          )}`}
                        >
                          {getStatusText(
                            application.status,
                            application.isCompleted
                          )}
                        </span>
                      </td>

                      {/* Submission Date */}
                      <td className="py-4 px-4 text-center">
                        <div className="flex flex-col items-center">
                          <span className="text-sm">
                            {application.submissionDate
                              ? new Date(
                                  application.submissionDate
                                ).toLocaleDateString()
                              : new Date(
                                  application.updatedAt
                                ).toLocaleDateString()}
                          </span>
                          <span className="text-xs text-gray-500">
                            {application.submissionDate
                              ? new Date(
                                  application.submissionDate
                                ).toLocaleTimeString()
                              : new Date(
                                  application.updatedAt
                                ).toLocaleTimeString()}
                          </span>
                        </div>
                      </td>

                      {/* Actions */}
                      <td className="py-4 px-4">
                        <div className="flex items-center justify-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 border-red-300 hover:bg-red-50"
                            onClick={() =>
                              handleDeleteApplication(application.id)
                            }
                          >
                            Delete
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleViewApplication(application.id)
                            }
                          >
                            View
                          </Button>
                          <Button
                            size="sm"
                            className="bg-[#26355E] hover:bg-primary-navy/90 text-white"
                            onClick={() =>
                              handleContinueApplication(application.id)
                            }
                          >
                            Continue
                          </Button>
                        </div>
                      </td>
                    </tr>
                  </React.Fragment>
                ))}

                {/* Render Onboarding Users */}
                {onboardingUsers.map((onboardingUser: any) => (
                  <tr
                    key={onboardingUser.id || onboardingUser.Id}
                    className="border-b hover:bg-gray-50"
                  >
                    {/* Email & Phone Number */}
                    <td className="py-4 px-4">
                      <div className="flex flex-col space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-900">
                            Email:
                          </span>
                          <span className="text-sm text-gray-600">
                            {onboardingUser.email}
                          </span>
                          <CircleCheck
                            size={12}
                            className={
                              onboardingUser.isEmailverified
                                ? "text-green-500"
                                : "text-gray-400"
                            }
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-900">
                            Phone:
                          </span>
                          <span className="text-sm text-gray-600">
                            {onboardingUser.phone_no}
                          </span>
                          <CircleCheck
                            size={12}
                            className={
                              onboardingUser.isphoneverified
                                ? "text-green-500"
                                : "text-gray-400"
                            }
                          />
                        </div>
                      </div>
                    </td>

                    {/* Facial Verification */}
                    <td className="py-4 px-4 text-center">
                      <span className="px-3 py-1 rounded-full text-sm border bg-gray-50 text-gray-700 border-gray-200">
                        Not Validated
                      </span>
                    </td>

                    {/* KYC Status */}
                    <td className="py-4 px-4 text-center">
                      <span className="px-3 py-1 rounded-full text-sm border bg-orange-50 text-orange-700 border-orange-200">
                        Pending Verification
                      </span>
                    </td>

                    {/* Submission Date */}
                    <td className="py-4 px-4 text-center">
                      <div className="flex flex-col items-center">
                        <span className="text-sm">
                          {new Date(
                            onboardingUser.DateOfCreation ||
                              onboardingUser.ModifiedDate
                          ).toLocaleDateString()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(
                            onboardingUser.DateOfCreation ||
                              onboardingUser.ModifiedDate
                          ).toLocaleTimeString()}
                        </span>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="py-4 px-4">
                      <div className="flex items-center justify-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 border-red-300 hover:bg-red-50"
                          onClick={() =>
                            handleDeleteOnboardingUser(
                              getOnboardingUserId(onboardingUser)
                            )
                          }
                        >
                          Delete
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            handleViewApplication(
                              getOnboardingUserId(onboardingUser)
                            )
                          }
                        >
                          View
                        </Button>
                        <Button
                          size="sm"
                          className="bg-[#26355E] hover:bg-primary-navy/90 text-white"
                          onClick={() =>
                            handleContinueApplication(
                              getOnboardingUserId(onboardingUser)
                            )
                          }
                        >
                          Continue
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedKycDashboard;
