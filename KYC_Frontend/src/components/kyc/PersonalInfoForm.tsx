import React from "react";
import { Input } from "@/components/ui/input";
import { PersonalInfo } from "@/redux/slices/kycSlice";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface PersonalInfoFormProps {
  formData: PersonalInfo;
  errors: {
    name?: string;
    // lastName?: string;
    // email?: string;
    // mobile?: string;
    date_of_birth?: string;
    gender?: string;
    father_name?: string;
    mother_name?: string;
    spouse_name?: string;
    no_of_dependent?: string;
    name_of_guardian?: string;
    relation_with_gurdian?: string;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange?: (name: string, value: string) => void;
  handleCheckboxChange?: (name: string, checked: boolean) => void;
  handleNumberChange?: (name: string, value: number) => void;
}

const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  formData,
  errors,
  handleChange,
  handleSelectChange = () => {},
  handleCheckboxChange = () => {},
  handleNumberChange = () => {},
}) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Input
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="First name"
            className={`h-12 ${errors.name ? "border-red-500" : ""}`}
          />
          {errors.name && (
            <p className="mt-1 text-xs text-red-500">{errors.name}</p>
          )}
        </div>
        {/* <div>
          <Input
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            placeholder="Last name"
            className={`h-12 ${errors.lastName ? "border-red-500" : ""}`}
          />
          {errors.lastName && (
            <p className="mt-1 text-xs text-red-500">{errors.lastName}</p>
          )}
        </div> */}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <Input
            name="date_of_birth"
            value={formData.date_of_birth}
            onChange={handleChange}
            placeholder="date_of_birth: YYY/MM/DD"
            className={`h-12 ${errors.date_of_birth ? "border-red-500" : ""}`}
          />
          {errors.date_of_birth && (
            <p className="mt-1 text-xs text-red-500">{errors.date_of_birth}</p>
          )}
        </div>
        <div>
          <Select
            value={formData.gender}
            onValueChange={(value) => handleSelectChange("gender", value)}
          >
            <SelectTrigger
              className={`h-12 ${errors.gender ? "border-red-500" : ""}`}
            >
              <SelectValue placeholder="Select Gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          {errors.gender && (
            <p className="mt-1 text-xs text-red-500">{errors.gender}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <Input
            name="father_name"
            value={formData.father_name}
            onChange={handleChange}
            placeholder="Father's Name"
            className={`h-12 ${errors.father_name ? "border-red-500" : ""}`}
          />
          {errors.father_name && (
            <p className="mt-1 text-xs text-red-500">{errors.father_name}</p>
          )}
        </div>
        <div>
          <Input
            name="mother_name"
            value={formData.mother_name}
            onChange={handleChange}
            placeholder="Mother's Name"
            className={`h-12 ${errors.mother_name ? "border-red-500" : ""}`}
          />
          {errors.mother_name && (
            <p className="mt-1 text-xs text-red-500">{errors.mother_name}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <div className="flex items-center bg-[#F7FAFC] rounded-xl border border-[#E2E8F0] px-4 py-3 h-12 justify-between">
            <Label htmlFor="martial_status" className="text-base font-normal">
              Fill checkbox if married
            </Label>
            <Checkbox
              id="martial_status"
              checked={formData.martial_status}
              onCheckedChange={(checked) =>
                handleCheckboxChange("martial_status", checked as boolean)
              }
              className="ml-auto"
            />
          </div>
        </div>
        <div>
          <Input
            name="spouse_name"
            value={formData.spouse_name}
            onChange={handleChange}
            placeholder="Spouse name"
            className={`h-12 ${errors.spouse_name ? "border-red-500" : ""}`}
            disabled={!formData.martial_status}
          />
          {errors.spouse_name && (
            <p className="mt-1 text-xs text-red-500">{errors.spouse_name}</p>
          )}
        </div>
      </div>

      <div className="mt-4">
        <div className="flex items-center bg-[#F7FAFC] rounded-xl border border-[#E2E8F0] px-4 py-3 h-12 w-full">
          <span className="text-base flex-1">No. of dependent</span>
          <button
            type="button"
            className="border rounded-l px-2 py-1 bg-white"
            onClick={() =>
              handleNumberChange(
                "no_of_dependent",
                Math.max(0, formData.no_of_dependent - 1)
              )
            }
          >
            &lt;
          </button>
          <input
            type="number"
            id="no_of_dependent"
            value={formData.no_of_dependent}
            onChange={(e) =>
              handleNumberChange(
                "no_of_dependent",
                parseInt(e.target.value) || 0
              )
            }
            className="border-t border-b text-center w-10 py-1 bg-white"
            min="0"
            style={{ outline: "none" }}
          />
          <button
            type="button"
            className="border rounded-r px-2 py-1 bg-white"
            onClick={() =>
              handleNumberChange(
                "no_of_dependent",
                formData.no_of_dependent + 1
              )
            }
          >
            &gt;
          </button>
        </div>
        {errors.no_of_dependent && (
          <p className="mt-1 text-xs text-red-500">{errors.no_of_dependent}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div>
          <Input
            name="name_of_guardian"
            value={formData.name_of_guardian}
            onChange={handleChange}
            placeholder="Name of guardian"
            className={`h-12 ${
              errors.name_of_guardian ? "border-red-500" : ""
            }`}
          />
          {errors.name_of_guardian && (
            <p className="mt-1 text-xs text-red-500">
              {errors.name_of_guardian}
            </p>
          )}
        </div>
        <div>
          <Select
            value={formData.relation_with_gurdian}
            onValueChange={(value) =>
              handleSelectChange("relation_with_gurdian", value)
            }
          >
            <SelectTrigger
              className={`h-12 ${
                errors.relation_with_gurdian ? "border-red-500" : ""
              }`}
            >
              <SelectValue placeholder="Relation with guardian" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="parent">Parent</SelectItem>
              <SelectItem value="sibling">Sibling</SelectItem>
              <SelectItem value="relative">Relative</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          {errors.relation_with_gurdian && (
            <p className="mt-1 text-xs text-red-500">
              {errors.relation_with_gurdian}
            </p>
          )}
        </div>
      </div>
    </>
  );
};

export default PersonalInfoForm;
