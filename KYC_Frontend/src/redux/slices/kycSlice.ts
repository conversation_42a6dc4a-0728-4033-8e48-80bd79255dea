import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import {
  PersonalInfo,
  AdditionalInfo,
  AddressDetails,
  VerificationStatus,
  FormProgress,
  KYCApplication,
  KYCState,
  PersonalInformationPayload,
  AddressInformationPayload,
  DocumentInformationPayload,
  ApiData,
  AddressInfoRecord,
  DocumentInfoRecord,
  PersonalInfoRecord,
} from "../../types/index";

// Types are now imported from centralized location

// Types are now imported from centralized location

export interface AddressSubmissionData {
  temporary: AddressInfoRecord;
  permanent: AddressInfoRecord;
}

// Types are now imported from centralized location

// FormProgress is now imported from centralized types

// KYCApplication is now imported from centralized types

// KYCState is now imported from centralized types

// Initial form progress state
const initialFormProgress: FormProgress = {
  personalInfo: {
    completed: false,
    progress: 0,
  },
  additionalInfo: {
    completed: false,
    progress: 0,
  },
  addressInfo: {
    completed: false,
    progress: 0,
  },
  documentInfo: {
    completed: false,
    documentsUploaded: 0,
    totalDocumentsRequired: 3, // Adjust based on requirements
    progress: 0,
  },
  facialVerification: {
    completed: false,
    progress: 0,
  },
};

const initialState: KYCState = {
  personalInfo: {
    name: "",
    date_of_birth: "",
    gender: "",
    father_name: "",
    mother_name: "",
    martial_status: false,
    spouse_name: "",
    no_of_dependent: 0,
    name_of_guardian: "",
    relation_with_gurdian: "",
  },
  addressDetails: {
    temporary: {
      street: "",
      city: "",
      state: "",
      pincode: "",
    },
    permanent: {
      street: "",
      city: "",
      state: "",
      pincode: "",
    },
  },
  additionalInfo: {
    nationality: "",
    religion: "",
    country_name: "",
    citizenship: "",
    persion_with_disability: false,
    education: "",
    occupation: "",
    organisation_name: "",
    designation_profession: "",
    net_worth: "",
    nature_of_bussiness: "",
  },
  verificationStatus: {
    email: false,
    mobile: false,
    facial: false,
    documents: {},
  },
  currentStep: 1,
  isCompleted: false,
  completionDate: null,
  formProgress: { ...initialFormProgress },

  // Multi-client support
  applications: [],
  currentApplicationId: null,
  isSubmitting: false,
  submissionError: null,

  // API integration states
  apiLoading: {
    personalInfo: false,
    additionalInfo: false,
    addressInfo: false,
    documentInfo: false,
    fetchingApplications: false,
  },
  apiErrors: {
    personalInfo: null,
    additionalInfo: null,
    addressInfo: null,
    documentInfo: null,
    fetchingApplications: null,
  },
  apiData: {
    personalInfoRecords: [],
    addressInfoRecords: [],
    documentInfoRecords: [],
    lastSyncTimestamp: null,
  },

  // Dashboard refresh trigger
  dashboardRefreshTrigger: 0,
};

// Load KYC data from localStorage if available
const loadKYCFromStorage = (): KYCState => {
  const kycJSON = localStorage.getItem("kyc_data");
  if (kycJSON) {
    try {
      const storedData = JSON.parse(kycJSON);
      // Merge with initial state to ensure all properties exist
      // This handles cases where localStorage has old data structure
      return {
        ...initialState,
        ...storedData,
        // Ensure applications array exists
        applications: storedData.applications || [],
      };
    } catch (error) {
      console.error("Failed to parse KYC data from localStorage", error);
    }
  }
  return initialState;
};

const storedKYC = loadKYCFromStorage();

export const kycSlice = createSlice({
  name: "kyc",
  initialState: storedKYC,
  reducers: {
    updatePersonalInfo: (
      state,
      action: PayloadAction<Partial<PersonalInfo>>
    ) => {
      state.personalInfo = { ...state.personalInfo, ...action.payload };
      // console.log("Personal info updated", state.personalInfo);
      // console.log("Personal info Action", action.payload);

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    updateAddressDetails: (
      state,
      action: PayloadAction<Partial<AddressDetails>>
    ) => {
      state.addressDetails = { ...state.addressDetails, ...action.payload };
      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    updateAdditionalInfo: (
      state,
      action: PayloadAction<Partial<AdditionalInfo>>
    ) => {
      state.additionalInfo = { ...state.additionalInfo, ...action.payload };
      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    verifyEmail: (state, action: PayloadAction<boolean>) => {
      state.verificationStatus.email = action.payload;
      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    verifyMobile: (state, action: PayloadAction<boolean>) => {
      state.verificationStatus.mobile = action.payload;
      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    verifyFacial: (state, action: PayloadAction<boolean>) => {
      state.verificationStatus.facial = action.payload;
      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    addDocument: (
      state,
      action: PayloadAction<{
        id: string;
        file: string;
        type: string;
        name: string;
        verified: boolean;
      }>
    ) => {
      const { id, file, type, name, verified } = action.payload;
      state.verificationStatus.documents[id] = { file, type, name, verified };
      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    verifyDocument: (
      state,
      action: PayloadAction<{ id: string; verified: boolean }>
    ) => {
      const { id, verified } = action.payload;
      if (state.verificationStatus.documents[id]) {
        state.verificationStatus.documents[id].verified = verified;
        localStorage.setItem("kyc_data", JSON.stringify(state));
      }
    },
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;

      // Update the current application if it exists
      if (state.currentApplicationId) {
        const applicationIndex = state.applications.findIndex(
          (app) => app.id === state.currentApplicationId
        );
        if (applicationIndex !== -1) {
          state.applications[applicationIndex].currentStep = action.payload;
          // Update timestamp
          state.applications[applicationIndex].updatedAt =
            new Date().toISOString();
        }
      }

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },
    completeKYC: (state) => {
      // Only mark as completed if we're at the final step
      if (state.currentStep >= 10) {
        state.isCompleted = true;
        state.completionDate = new Date().toLocaleDateString();

        // Update the current application if it exists
        if (state.currentApplicationId) {
          const applicationIndex = state.applications.findIndex(
            (app) => app.id === state.currentApplicationId
          );
          if (applicationIndex !== -1) {
            state.applications[applicationIndex].isCompleted = true;
            state.applications[applicationIndex].completionDate =
              state.completionDate;
            state.applications[applicationIndex].status = "submitted";
          }
        }

        localStorage.setItem("kyc_data", JSON.stringify(state));
      }
    },
    resetKYC: () => {
      localStorage.removeItem("kyc_data");
      return initialState;
    },

    // Progress validation and recovery
    validateAndFixProgress: (state) => {
      // Fix any inconsistencies in the current application
      if (state.currentApplicationId) {
        const applicationIndex = state.applications.findIndex(
          (app) => app.id === state.currentApplicationId
        );
        if (applicationIndex !== -1) {
          const application = state.applications[applicationIndex];

          // If marked as completed but not at final step, fix it
          if (application.isCompleted && application.currentStep < 10) {
            state.applications[applicationIndex].isCompleted = false;
            state.applications[applicationIndex].completionDate = null;
            state.applications[applicationIndex].status = "in_progress";

            // Update current state too
            state.isCompleted = false;
            state.completionDate = null;
          }

          // If at final step but not marked as completed, fix it
          if (!application.isCompleted && application.currentStep >= 10) {
            state.applications[applicationIndex].isCompleted = true;
            state.applications[applicationIndex].completionDate =
              new Date().toLocaleDateString();
            state.applications[applicationIndex].status = "submitted";

            // Update current state too
            state.isCompleted = true;
            state.completionDate = new Date().toLocaleDateString();
          }
        }
      }

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },

    // Multi-client actions
    createNewApplication: (
      state,
      action: PayloadAction<{
        email: string;
        phone: string;
        clientName?: string;
      }>
    ) => {
      const now = new Date().toISOString();
      const newApplication: KYCApplication = {
        id: Date.now().toString(), // Simple ID generation
        email: action.payload.email,
        phone: action.payload.phone,
        clientName: action.payload.clientName || `${action.payload.email}`, // Use email as fallback
        personalInfo: {
          ...initialState.personalInfo,
        },
        addressDetails: { ...initialState.addressDetails },
        additionalInfo: { ...initialState.additionalInfo },
        verificationStatus: { ...initialState.verificationStatus },
        currentStep: 1,
        isCompleted: false,
        completionDate: null,
        submissionDate: null,
        createdAt: now,
        updatedAt: now,
        status: "draft",
        formProgress: { ...initialFormProgress }, // Initialize form progress
      };

      state.applications.push(newApplication);
      state.currentApplicationId = newApplication.id;

      // Reset current form to new application
      state.personalInfo = { ...newApplication.personalInfo };
      state.addressDetails = { ...newApplication.addressDetails };
      state.additionalInfo = { ...newApplication.additionalInfo };
      state.verificationStatus = { ...newApplication.verificationStatus };
      state.currentStep = newApplication.currentStep;
      state.isCompleted = newApplication.isCompleted;
      state.completionDate = newApplication.completionDate;
      state.formProgress = { ...newApplication.formProgress };

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },

    switchToApplication: (state, action: PayloadAction<string>) => {
      const application = state.applications.find(
        (app) => app.id === action.payload
      );
      if (application) {
        state.currentApplicationId = application.id;
        state.personalInfo = { ...application.personalInfo };
        state.addressDetails = { ...application.addressDetails };
        state.additionalInfo = { ...application.additionalInfo };
        state.verificationStatus = { ...application.verificationStatus };
        state.currentStep = application.currentStep;
        state.isCompleted = application.isCompleted;
        state.completionDate = application.completionDate;
        state.formProgress = application.formProgress || {
          ...initialFormProgress,
        }; // Handle legacy applications

        localStorage.setItem("kyc_data", JSON.stringify(state));
      }
    },

    saveCurrentApplication: (state) => {
      if (state.currentApplicationId) {
        const applicationIndex = state.applications.findIndex(
          (app) => app.id === state.currentApplicationId
        );
        if (applicationIndex !== -1) {
          // Update clientName if firstName and lastName are available
          let clientName = state.applications[applicationIndex].clientName;
          if (state.personalInfo.name && state.personalInfo.name) {
            clientName = `${state.personalInfo.name} ${state.personalInfo.name}`;
          }

          state.applications[applicationIndex] = {
            ...state.applications[applicationIndex],
            // email:
            //   state.personalInfo.email ||
            //   state.applications[applicationIndex].email,
            // phone:
            //   state.personalInfo.mobile ||
            //   state.applications[applicationIndex].phone,
            clientName,
            personalInfo: { ...state.personalInfo },
            addressDetails: { ...state.addressDetails },
            additionalInfo: { ...state.additionalInfo },
            verificationStatus: { ...state.verificationStatus },
            currentStep: state.currentStep,
            isCompleted: state.isCompleted,
            completionDate: state.completionDate,
            updatedAt: new Date().toISOString(),
          };

          localStorage.setItem("kyc_data", JSON.stringify(state));
        }
      }
    },

    setSubmissionLoading: (state, action: PayloadAction<boolean>) => {
      state.isSubmitting = action.payload;
      state.submissionError = null;
    },

    setSubmissionSuccess: (
      state,
      action: PayloadAction<{ applicationId: string; apiId: string }>
    ) => {
      state.isSubmitting = false;
      state.submissionError = null;

      const applicationIndex = state.applications.findIndex(
        (app) => app.id === action.payload.applicationId
      );
      if (applicationIndex !== -1) {
        state.applications[applicationIndex].status = "submitted";
        state.applications[applicationIndex].submissionDate =
          new Date().toISOString();
        state.applications[applicationIndex].apiSubmissionId =
          action.payload.apiId;

        // Only mark as completed if we're at the final step (step 10)
        if (state.applications[applicationIndex].currentStep >= 10) {
          state.applications[applicationIndex].isCompleted = true;
          state.applications[applicationIndex].completionDate =
            new Date().toLocaleDateString();
        }
      }

      // Update current state if this is the active application
      if (state.currentApplicationId === action.payload.applicationId) {
        // Only mark current state as completed if we're at the final step
        if (state.currentStep >= 10) {
          state.isCompleted = true;
          state.completionDate = new Date().toLocaleDateString();
        }
      }

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },

    setSubmissionError: (state, action: PayloadAction<string>) => {
      state.isSubmitting = false;
      state.submissionError = action.payload;
    },

    deleteApplication: (state, action: PayloadAction<string>) => {
      const applicationIndex = state.applications.findIndex(
        (app) => app.id === action.payload
      );
      if (applicationIndex !== -1) {
        state.applications.splice(applicationIndex, 1);

        // If this was the current application, switch to another or reset
        if (state.currentApplicationId === action.payload) {
          if (state.applications.length > 0) {
            const newCurrent = state.applications[0];
            state.currentApplicationId = newCurrent.id;
            state.personalInfo = { ...newCurrent.personalInfo };
            state.addressDetails = { ...newCurrent.addressDetails };
            state.additionalInfo = { ...newCurrent.additionalInfo };
            state.verificationStatus = { ...newCurrent.verificationStatus };
            state.currentStep = newCurrent.currentStep;
            state.isCompleted = newCurrent.isCompleted;
            state.completionDate = newCurrent.completionDate;
          } else {
            // No applications left, reset to initial state
            state.currentApplicationId = null;
            state.personalInfo = { ...initialState.personalInfo };
            state.addressDetails = { ...initialState.addressDetails };
            state.additionalInfo = { ...initialState.additionalInfo };
            state.verificationStatus = { ...initialState.verificationStatus };
            state.currentStep = 1;
            state.isCompleted = false;
            state.completionDate = null;
          }
        }

        localStorage.setItem("kyc_data", JSON.stringify(state));
      }
    },

    // API Integration Actions
    setApiLoading: (
      state,
      action: PayloadAction<{
        type:
          | "personalInfo"
          | "addressInfo"
          | "documentInfo"
          | "fetchingApplications";
        loading: boolean;
      }>
    ) => {
      state.apiLoading[action.payload.type] = action.payload.loading;
      if (action.payload.loading) {
        // Clear previous error when starting new request
        state.apiErrors[action.payload.type] = null;
      }
    },

    setApiError: (
      state,
      action: PayloadAction<{
        type:
          | "personalInfo"
          | "addressInfo"
          | "documentInfo"
          | "fetchingApplications";
        error: string;
      }>
    ) => {
      state.apiLoading[action.payload.type] = false;
      state.apiErrors[action.payload.type] = action.payload.error;
    },

    clearApiError: (
      state,
      action: PayloadAction<
        "personalInfo" | "addressInfo" | "documentInfo" | "fetchingApplications"
      >
    ) => {
      state.apiErrors[action.payload] = null;
    },

    setPersonalInfoRecords: (
      state,
      action: PayloadAction<PersonalInfoRecord[]>
    ) => {
      state.apiData.personalInfoRecords = action.payload;
      state.apiData.lastSyncTimestamp = new Date().toISOString();
      state.apiLoading.personalInfo = false;
      state.apiErrors.personalInfo = null;
    },

    setAddressInfoRecords: (
      state,
      action: PayloadAction<AddressInfoRecord[]>
    ) => {
      state.apiData.addressInfoRecords = action.payload;
      state.apiData.lastSyncTimestamp = new Date().toISOString();
      state.apiLoading.addressInfo = false;
      state.apiErrors.addressInfo = null;
    },

    setDocumentInfoRecords: (
      state,
      action: PayloadAction<DocumentInfoRecord[]>
    ) => {
      state.apiData.documentInfoRecords = action.payload;
      state.apiData.lastSyncTimestamp = new Date().toISOString();
      state.apiLoading.documentInfo = false;
      state.apiErrors.documentInfo = null;
    },

    syncApplicationWithApi: (
      state,
      action: PayloadAction<{
        applicationId: string;
        apiData: PersonalInfoRecord | AddressInfoRecord | DocumentInfoRecord;
        apiId: string;
      }>
    ) => {
      const applicationIndex = state.applications.findIndex(
        (app) => app.id === action.payload.applicationId
      );
      if (applicationIndex !== -1) {
        state.applications[applicationIndex].apiSubmissionId =
          action.payload.apiId;
        state.applications[applicationIndex].status = "submitted";
        state.applications[applicationIndex].submissionDate =
          new Date().toISOString();

        // Update current state if this is the active application
        if (state.currentApplicationId === action.payload.applicationId) {
          // Only mark as completed if we're at the final step
          if (state.currentStep >= 10) {
            state.isCompleted = true;
            state.completionDate = new Date().toLocaleDateString();
          }
        }

        localStorage.setItem("kyc_data", JSON.stringify(state));
      }
    },

    // Address API Success Action
    setAddressSubmissionSuccess: (
      state,
      action: PayloadAction<{
        applicationId: string;
        apiId: string;
        apiData: AddressSubmissionData;
      }>
    ) => {
      const applicationIndex = state.applications.findIndex(
        (app) => app.id === action.payload.applicationId
      );
      if (applicationIndex !== -1) {
        // Store address API ID in the application
        if (
          !state.applications[applicationIndex].apiSubmissionId ||
          typeof state.applications[applicationIndex].apiSubmissionId ===
            "string"
        ) {
          state.applications[applicationIndex].apiSubmissionId = {};
        }
        (
          state.applications[applicationIndex].apiSubmissionId as {
            personalId?: string;
            addressId?: string;
            documentIds?: Record<string, string>;
          }
        ).addressId = action.payload.apiId;

        localStorage.setItem("kyc_data", JSON.stringify(state));
      }

      // Clear loading and error states
      state.apiLoading.addressInfo = false;
      state.apiErrors.addressInfo = null;
    },

    // Document API Success Action
    setDocumentSubmissionSuccess: (
      state,
      action: PayloadAction<{
        applicationId: string;
        apiId: string;
        apiData: DocumentInfoRecord;
        documentType: string;
      }>
    ) => {
      const applicationIndex = state.applications.findIndex(
        (app) => app.id === action.payload.applicationId
      );
      if (applicationIndex !== -1) {
        // Store document API ID in the application
        if (
          !state.applications[applicationIndex].apiSubmissionId ||
          typeof state.applications[applicationIndex].apiSubmissionId ===
            "string"
        ) {
          state.applications[applicationIndex].apiSubmissionId = {};
        }
        const apiIds = state.applications[applicationIndex].apiSubmissionId as {
          personalId?: string;
          addressId?: string;
          documentIds?: Record<string, string>;
        };
        if (!apiIds.documentIds) {
          apiIds.documentIds = {};
        }
        apiIds.documentIds[action.payload.documentType] = action.payload.apiId;

        localStorage.setItem("kyc_data", JSON.stringify(state));
      }

      // Clear loading and error states
      state.apiLoading.documentInfo = false;
      state.apiErrors.documentInfo = null;
    },

    // Form progress tracking actions
    setPersonalInfoProgress: (
      state,
      action: PayloadAction<{
        completed: boolean;
        apiId?: string;
        progress?: number;
      }>
    ) => {
      const now = new Date().toISOString();
      state.formProgress.personalInfo = {
        completed: action.payload.completed,
        completedAt: action.payload.completed ? now : undefined,
        apiId: action.payload.apiId,
        progress:
          action.payload.progress || (action.payload.completed ? 100 : 0),
      };

      // Update current application if it exists
      if (state.currentApplicationId) {
        const applicationIndex = state.applications.findIndex(
          (app) => app.id === state.currentApplicationId
        );
        if (applicationIndex !== -1) {
          state.applications[applicationIndex].formProgress.personalInfo = {
            ...state.formProgress.personalInfo,
          };
          state.applications[applicationIndex].updatedAt = now;
        }
      }

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },

    setAdditionalInfoProgress: (
      state,
      action: PayloadAction<{
        completed: boolean;
        apiId?: string;
        progress?: number;
      }>
    ) => {
      const now = new Date().toISOString();
      state.formProgress.additionalInfo = {
        completed: action.payload.completed,
        completedAt: action.payload.completed ? now : undefined,
        apiId: action.payload.apiId,
        progress:
          action.payload.progress || (action.payload.completed ? 100 : 0),
      };

      // Update current application if it exists
      if (state.currentApplicationId) {
        const applicationIndex = state.applications.findIndex(
          (app) => app.id === state.currentApplicationId
        );
        if (applicationIndex !== -1) {
          state.applications[applicationIndex].formProgress.additionalInfo = {
            ...state.formProgress.additionalInfo,
          };
          state.applications[applicationIndex].updatedAt = now;
        }
      }

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },

    setAddressInfoProgress: (
      state,
      action: PayloadAction<{
        completed: boolean;
        apiId?: string;
        progress?: number;
      }>
    ) => {
      const now = new Date().toISOString();
      state.formProgress.addressInfo = {
        completed: action.payload.completed,
        completedAt: action.payload.completed ? now : undefined,
        apiId: action.payload.apiId,
        progress:
          action.payload.progress || (action.payload.completed ? 100 : 0),
      };

      // Update current application if it exists
      if (state.currentApplicationId) {
        const applicationIndex = state.applications.findIndex(
          (app) => app.id === state.currentApplicationId
        );
        if (applicationIndex !== -1) {
          state.applications[applicationIndex].formProgress.addressInfo = {
            ...state.formProgress.addressInfo,
          };
          state.applications[applicationIndex].updatedAt = now;
        }
      }

      localStorage.setItem("kyc_data", JSON.stringify(state));
    },

    // Dashboard refresh trigger
    triggerDashboardRefresh: (state) => {
      state.dashboardRefreshTrigger = Date.now();
    },
  },
});

export const {
  updatePersonalInfo,
  updateAddressDetails,
  updateAdditionalInfo,
  verifyEmail,
  verifyMobile,
  verifyFacial,
  addDocument,
  verifyDocument,
  setCurrentStep,
  completeKYC,
  resetKYC,
  validateAndFixProgress,
  createNewApplication,
  switchToApplication,
  saveCurrentApplication,
  setSubmissionLoading,
  setSubmissionSuccess,
  setSubmissionError,
  deleteApplication,
  // API Integration Actions
  setApiLoading,
  setApiError,
  clearApiError,
  setPersonalInfoRecords,
  setAddressInfoRecords,
  setDocumentInfoRecords,
  syncApplicationWithApi,
  setAddressSubmissionSuccess,
  setDocumentSubmissionSuccess,
  // Form Progress Actions
  setPersonalInfoProgress,
  setAdditionalInfoProgress,
  setAddressInfoProgress,
  triggerDashboardRefresh,
} = kycSlice.actions;

export const selectPersonalInfo = (state: RootState) => state.kyc.personalInfo;
export const selectAddressDetails = (state: RootState) =>
  state.kyc.addressDetails;
export const selectAdditionalInfo = (state: RootState) =>
  state.kyc.additionalInfo;
export const selectVerificationStatus = (state: RootState) =>
  state.kyc.verificationStatus;
export const selectCurrentStep = (state: RootState) => state.kyc.currentStep;
export const selectIsKYCCompleted = (state: RootState) => state.kyc.isCompleted;
export const selectKYCCompletionDate = (state: RootState) =>
  state.kyc.completionDate;

// Multi-client selectors
export const selectKYCApplications = (state: RootState) =>
  state.kyc.applications;
export const selectCurrentApplicationId = (state: RootState) =>
  state.kyc.currentApplicationId;
export const selectCurrentApplication = (state: RootState) => {
  if (!state.kyc.currentApplicationId) return null;
  return (
    state.kyc.applications?.find(
      (app) => app.id === state.kyc.currentApplicationId
    ) || null
  );
};
export const selectIsSubmitting = (state: RootState) => state.kyc.isSubmitting;
export const selectSubmissionError = (state: RootState) =>
  state.kyc.submissionError;
export const selectApplicationById = (state: RootState, id: string) =>
  state.kyc.applications?.find((app) => app.id === id);
export const selectCompletedApplications = (state: RootState) =>
  state.kyc.applications?.filter((app) => app.isCompleted) || [];
export const selectPendingApplications = (state: RootState) =>
  state.kyc.applications?.filter((app) => !app.isCompleted) || [];
export const selectVerifiedApplications = (state: RootState) =>
  state.kyc.applications?.filter(
    (app) => app.verificationStatus.email && app.verificationStatus.mobile
  ) || [];

// API Integration Selectors
export const selectApiLoading = (state: RootState) => state.kyc.apiLoading;
export const selectApiErrors = (state: RootState) => state.kyc.apiErrors;
export const selectApiData = (state: RootState) => state.kyc.apiData;
export const selectPersonalInfoRecords = (state: RootState) =>
  state.kyc.apiData.personalInfoRecords;
export const selectAddressInfoRecords = (state: RootState) =>
  state.kyc.apiData.addressInfoRecords;
export const selectDocumentInfoRecords = (state: RootState) =>
  state.kyc.apiData.documentInfoRecords;
export const selectLastSyncTimestamp = (state: RootState) =>
  state.kyc.apiData.lastSyncTimestamp;
export const selectDashboardRefreshTrigger = (state: RootState) =>
  state.kyc.dashboardRefreshTrigger;

export default kycSlice.reducer;
