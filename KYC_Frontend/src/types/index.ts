// Centralized Type Definitions for KYC Application
// This file consolidates all TypeScript types used across the application

// ============================================================================
// USER TYPES
// ============================================================================

export interface User {
  user_id?: string;
  name?: string;
  email?: string;
  phone_no?: string;
  password?: string;
  gender?: string;
  profile_pic?: string;
  isEmailverified?: boolean;
  isphoneverified?: boolean;
  date_joined?: string;
  // Legacy fields for backward compatibility
  id?: string;
  firstName?: string;
  lastName?: string;
  profilePicture?: string;
  hasSkippedProfilePicture?: boolean;
}

export interface LoginResponse {
  user?: User;
  access_token?: string;
  refresh_token?: string;
  IsSucess?: boolean;
  IsSuccess?: boolean; // Alternative spelling
  status?: boolean;
  message?: string; // Error message
  error?: string; // Alternative error field
  detail?: string; // Django error field
}

export interface SignupResponse {
  IsSucess?: boolean;
  IsSuccess?: boolean; // Alternative spelling
  user?: User;
  Description?: string;
  message?: string; // Alternative message field
}

export interface OTPResponse {
  success: boolean;
  message: string;
}

// ============================================================================
// KYC FORM TYPES
// ============================================================================

export interface PersonalInfo {
  name: string;
  date_of_birth: string;
  gender: string;
  father_name: string;
  mother_name: string;
  martial_status: boolean;
  spouse_name?: string;
  no_of_dependent: number;
  name_of_guardian?: string;
  relation_with_gurdian?: string;
}

export interface AddressDetails {
  temporary: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  permanent: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
}

export interface AdditionalInfo {
  nationality: string;
  country_name: string;
  citizenship: string;
  religion: string;
  persion_with_disability: boolean;
  education: string;
  occupation: string;
  organisation_name: string;
  designation_profession: string;
  nature_of_bussiness: string;
  net_worth: string; // Form input as string, converted to number for API
}

// ============================================================================
// API PAYLOAD TYPES
// ============================================================================

export interface OnboardingUserPayload {
  user_id: string;
  email: string;
  phone_no: string;
  isEmailverified: boolean;
  isphoneverified: boolean;
  isProfilePicVerified: boolean;
  DateOfCreation?: string;
  ModifiedDate?: string;
  // Additional possible ID fields that might be returned from API
  id?: string;
  Id?: string;
  ID?: string;
  userId?: string;
  User_id?: string;
}

// Unified payload for Personal + Additional Information (matches exact API format)
export interface PersonalInformationPayload {
  id?: string;
  name: string;
  date_of_birth: string;
  gender: string;
  father_name: string;
  mother_name: string;
  martial_status: boolean;
  spouse_name?: string;
  no_of_dependent: number;
  name_of_guardian?: string;
  relation_with_gurdian?: string;
  nationality: string;
  country_name: string;
  citizenship: string;
  religion: string;
  persion_with_disability: boolean;
  education: string;
  occupation: string;
  organisation_name: string;
  designation_profession: string;
  nature_of_bussiness: string;
  net_worth: number;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

export interface AddressInformationPayload {
  id?: string;
  address_type: string;
  house_no?: string;
  street: string;
  city: string;
  state: string;
  country: string;
  pincode: number;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

export interface DocumentInformationPayload {
  id?: string;
  document_id?: number;
  document_type: string;
  document?: File | string;
  document_file?: File | string;
  is_verrified?: boolean;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface KYCSubmissionResponse {
  success: boolean;
  message: string;
  data?: unknown;
  id?: string;
}

export interface OnboardingUserResponse {
  success: boolean;
  message: string;
  data?: OnboardingUserPayload;
  id?: string;
}

// ============================================================================
// VERIFICATION AND DOCUMENT TYPES
// ============================================================================

export interface Document {
  id: string;
  type: string;
  file: string;
  name: string;
  verified: boolean;
}

export interface VerificationStatus {
  email: boolean;
  mobile: boolean;
  facial: boolean;
  documents: Record<
    string,
    {
      verified: boolean;
      file: string;
      type: string;
      name: string;
    }
  >;
}

// ============================================================================
// FORM PROGRESS AND APPLICATION TYPES
// ============================================================================

export interface FormProgress {
  personalInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
  additionalInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
  addressInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
  documentInfo: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    documentsUploaded: number;
    totalDocumentsRequired: number;
    progress: number; // 0-100
  };
  facialVerification: {
    completed: boolean;
    completedAt?: string;
    apiId?: string;
    progress: number; // 0-100
  };
}

export interface KYCApplication {
  id: string;
  email: string;
  phone: string;
  clientName: string;
  status:
    | "draft"
    | "pending"
    | "approved"
    | "rejected"
    | "in_progress"
    | "submitted";
  submissionDate: string | null;
  personalInfo: PersonalInfo;
  additionalInfo: AdditionalInfo;
  addressDetails: AddressDetails;
  verificationStatus: VerificationStatus;
  currentStep: number;
  isCompleted: boolean;
  completionDate: string | null;
  createdAt: string;
  updatedAt: string;
  formProgress: FormProgress;
  apiSubmissionId?:
    | string
    | {
        personalId?: string;
        addressId?: string;
        documentIds?: Record<string, string>;
      };
}

export interface KYCStatus {
  isCompleted: boolean;
  completionDate: string | null;
  personalInfo: PersonalInfo;
  addressDetails: AddressDetails;
  additionalInfo: AdditionalInfo;
  verificationStatus: {
    email: boolean;
    phone_no: boolean;
    facial: boolean;
    documents: Record<string, Document>;
  };
}

// ============================================================================
// REDUX STATE TYPES
// ============================================================================

export interface KYCState {
  // Current active application
  personalInfo: PersonalInfo;
  addressDetails: AddressDetails;
  additionalInfo: AdditionalInfo;
  verificationStatus: VerificationStatus;
  currentStep: number;
  isCompleted: boolean;
  completionDate: string | null;
  formProgress: FormProgress;

  // Multi-client support
  applications: KYCApplication[];
  currentApplicationId: string | null;
  isSubmitting: boolean;
  submissionError: string | null;

  // API integration states
  apiLoading: {
    personalInfo: boolean;
    additionalInfo: boolean;
    addressInfo: boolean;
    documentInfo: boolean;
    fetchingApplications: boolean;
  };
  apiErrors: {
    personalInfo: string | null;
    additionalInfo: string | null;
    addressInfo: string | null;
    documentInfo: string | null;
    fetchingApplications: string | null;
  };
  apiData: ApiData;

  // Dashboard refresh trigger
  dashboardRefreshTrigger: number;
}

// ============================================================================
// REDUX-SPECIFIC TYPES
// ============================================================================

export interface ApiData {
  personalInfoRecords: PersonalInfoRecord[];
  addressInfoRecords: AddressInfoRecord[];
  documentInfoRecords: DocumentInfoRecord[];
  lastSyncTimestamp: string | null;
}

// Redux-specific PersonalInfoRecord (for backward compatibility)
export interface PersonalInfoRecord {
  id?: string;
  name: string;
  date_of_birth: string;
  gender: string;
  father_name: string;
  mother_name: string;
  martial_status: boolean;
  spouse_name?: string;
  no_of_dependent: number;
  name_of_guardian?: string;
  relation_with_gurdian?: string;
  nationality: string;
  country_name: string;
  citizenship: string;
  religion: string;
  persion_with_disability: boolean;
  education: string;
  occupation: string;
  organisation_name: string;
  designation_profession: string;
  nature_of_bussiness: string;
  net_worth: number;
  DateOfCreation?: string;
  ModifiedDate?: string;
}

// Redux-specific record types (for backward compatibility)
export interface AddressInfoRecord {
  id?: string;
  address_type: string;
  house_no?: string;
  street: string;
  city: string;
  state: string;
  country: string;
  pincode: string; // String in Redux, number in API
  DateOfCreation?: string;
  ModifiedDate?: string;
}

export interface DocumentInfoRecord {
  id?: string;
  document_id?: number;
  document_type: string;
  document?: File | string;
  document_file?: File | string;
  is_verrified?: boolean;
  DateOfCreation?: string;
  ModifiedDate?: string;
}
