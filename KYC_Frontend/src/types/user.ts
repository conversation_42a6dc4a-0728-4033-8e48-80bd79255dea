export interface User {
  user_id?: string;
  name?: string;
  email?: string;
  phone_no?: string;
  password?: string;
  gender?: string;
  profile_pic?: string;
  isEmailverified?: boolean;
  isphoneverified?: boolean;
  date_joined?: string;
  // Legacy fields for backward compatibility
  id?: string;
  firstName?: string;
  lastName?: string;
  profilePicture?: string;
  hasSkippedProfilePicture?: boolean;
}

export interface LoginResponse {
  user?: User;
  access_token?: string;
  refresh_token?: string;
  IsSucess?: boolean;
  IsSuccess?: boolean; // Alternative spelling
  status?: boolean;
  message?: string; // Error message
  error?: string; // Alternative error field
  detail?: string; // Django error field
}

export interface SignupResponse {
  IsSucess?: boolean;
  IsSuccess?: boolean; // Alternative spelling
  user?: User;
  Description?: string;
  message?: string; // Alternative message field
}

export interface OTPResponse {
  success: boolean;
  message: string;
}

export interface PersonalInfo {
  name: string;
  date_of_birth: string;
  gender: string;
  father_name: string;
  mother_name: string;
  martial_status: boolean;
  spouse_name?: string;
  no_of_dependent: number;
  name_of_guardian?: string;
  relation_with_gurdian?: string;
}

export interface AddressDetails {
  temporary: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  permanent: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
}

export interface AdditionalInfo {
  nationality: string;
  religion: string;
}

export interface Document {
  id: string;
  type: string;
  file: string;
  name: string;
  verified: boolean;
}

export interface KYCStatus {
  isCompleted: boolean;
  completionDate: string | null;
  personalInfo: PersonalInfo;
  addressDetails: AddressDetails;
  additionalInfo: AdditionalInfo;
  verificationStatus: {
    email: boolean;
    phone_no: boolean;
    facial: boolean;
    documents: Record<string, Document>;
  };
}
