import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  updatePersonalInfo,
  selectPersonalInfo,
  setCurrentStep,
  saveCurrentApplication,
} from "@/redux/slices/kycSlice";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import PersonalInfoForm from "@/components/kyc/PersonalInfoForm";
import { kycService } from "@/services/kycService";

const PersonalInfo = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const storedPersonalInfo = useAppSelector(selectPersonalInfo);

  const [form, setForm] = useState({
    name: storedPersonalInfo.name || "",
    date_of_birth: storedPersonalInfo.date_of_birth || "",
    gender: storedPersonalInfo.gender || "",
    father_name: storedPersonalInfo.father_name || "",
    mother_name: storedPersonalInfo.mother_name || "",
    martial_status: storedPersonalInfo.martial_status || false,
    spouse_name: storedPersonalInfo.spouse_name || "",
    no_of_dependent: storedPersonalInfo.no_of_dependent || 0,
    name_of_guardian: storedPersonalInfo.name_of_guardian || "",
    relation_with_gurdian: storedPersonalInfo.relation_with_gurdian || "",
  });

  const [errors, setErrors] = useState({
    name: "",
    date_of_birth: "",
    gender: "",
    name_of_guardian: "",
    relation_with_gurdian: "",
    martial_status: "",
  });

  useEffect(() => {
    dispatch(setCurrentStep(2)); // Step 2: Personal Information
  }, [dispatch]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      date_of_birth: "",
      gender: "",
      father_name: "",
      mother_name: "",
      spouse_name: "",
      no_of_dependent: "",
      name_of_guardian: "",
      relation_with_gurdian: "",
      martial_status: "",
    };

    if (!form.name.trim()) {
      newErrors.name = "First name is required";
      isValid = false;
    }

    if (!form.date_of_birth.trim()) {
      newErrors.date_of_birth = "Date of birth is required";
      isValid = false;
    } else if (!/^\d{4}\/\d{2}\/\d{2}$/.test(form.date_of_birth)) {
      newErrors.date_of_birth = "Date format should be YYYY/MM/DD";
      isValid = false;
    }

    if (!form.gender) {
      newErrors.gender = "Gender is required";
      isValid = false;
    }

    if (!form.name_of_guardian.trim()) {
      newErrors.name_of_guardian = "Guardian name is required";
      isValid = false;
    }

    if (!form.relation_with_gurdian) {
      newErrors.relation_with_gurdian = "Guardian relation is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setForm((prev) => ({ ...prev, [name]: checked }));
  };

  const handleNumberChange = (name: string, value: number) => {
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // console.log("Form Data:", form);

    if (!validateForm()) return;

    // Save to Redux only, do not call API here
    dispatch(updatePersonalInfo(form));
    dispatch(setCurrentStep(3));
    dispatch(saveCurrentApplication());
    navigate("/kyc/additional");
  };

  const handleBack = () => {
    navigate("/kyc/verify-mobile");
  };

  return (
    <KYCLayout
      title="Personal Information"
      subtitle="Let's start with your basic identity details."
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <PersonalInfoForm
          formData={form}
          errors={errors}
          handleChange={handleChange}
          handleSelectChange={handleSelectChange}
          handleCheckboxChange={handleCheckboxChange}
          handleNumberChange={handleNumberChange}
        />

        <div className="flex justify-between pt-4">
          <Button
            type="button"
            variant="outline"
            className="border-[#26355E] text-[#26355E] hover:bg-[#26355E] hover:text-white"
            onClick={handleBack}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="bg-[#26355E] hover:bg-primary-navy/90 text-white px-8 py-2"
          >
            Next
          </Button>
        </div>
      </form>
    </KYCLayout>
  );
};

export default PersonalInfo;
