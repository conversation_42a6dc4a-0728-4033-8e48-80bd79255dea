import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  updateAdditionalInfo,
  selectAdditionalInfo,
  selectPersonalInfo,
  setCurrentStep,
  setSubmissionLoading,
  setSubmissionSuccess,
  setSubmissionError,
  selectCurrentApplicationId,
  selectIsSubmitting,
  selectSubmissionError,
  setApiLoading,
  setApiError,
  syncApplicationWithApi,
  selectApiLoading,
  selectApiErrors,
} from "@/redux/slices/kycSlice";
import { kycService } from "@/services/kycService";
import KYCLayout from "@/components/KYCLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const AdditionalInfo = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const personalInfo = useAppSelector(selectPersonalInfo);
  const storedAdditionalInfo = useAppSelector(selectAdditionalInfo);
  const currentApplicationId = useAppSelector(selectCurrentApplicationId);
  const isSubmitting = useAppSelector(selectIsSubmitting);
  const submissionError = useAppSelector(selectSubmissionError);
  const apiLoading = useAppSelector(selectApiLoading);
  const apiErrors = useAppSelector(selectApiErrors);

  const [form, setForm] = useState({
    nationality: storedAdditionalInfo.nationality || "",
    religion: storedAdditionalInfo.religion || "",
    country_name: storedAdditionalInfo.country_name || "",
    citizenship: storedAdditionalInfo.citizenship || "",
    persion_with_disability:
      storedAdditionalInfo.persion_with_disability || false,
    education: storedAdditionalInfo.education || "",
    occupation: storedAdditionalInfo.occupation || "",
    organisation_name: storedAdditionalInfo.organisation_name || "",
    designation_profession: storedAdditionalInfo.designation_profession || "",
    net_worth: storedAdditionalInfo.net_worth || "",
    // businessNature: storedAdditionalInfo.businessNature || "",
  });

  const [errors, setErrors] = useState({
    nationality: "",
    religion: "",
    country_name: "",
    citizenship: "",
    education: "",
    occupation: "",
    organisation_name: "",
    designation_profession: "",
    net_worth: "",
    // businessNature: "",
  });

  const nationalities = [
    "Indian",
    "Afghan",
    "Albanian",
    "Algerian",
    "American",
    "Andorran",
    "Angolan",
    "Antiguan",
    "Argentine",
    "Armenian",
    "Australian",
    "Austrian",
    "Azerbaijani",
    "Bahamian",
    "Bahraini",
    "Bangladeshi",
    "Barbadian",
    "Belarusian",
    "Belgian",
    "Belizean",
    "Beninese",
    "Bhutanese",
    "Bolivian",
    "Bosnian",
    "Brazilian",
    "British",
    "Bruneian",
    "Bulgarian",
    "Burkinabe",
    "Burmese",
    "Burundian",
  ];

  const religions = [
    "Hinduism",
    "Islam",
    "Christianity",
    "Sikhism",
    "Buddhism",
    "Jainism",
    "Judaism",
    "Zoroastrianism",
    "Other",
    "Prefer not to say",
  ];

  const countries = [
    "India",
    "United States",
    "United Kingdom",
    "Canada",
    "Australia",
    "Germany",
    "France",
    "Japan",
    "China",
    "Brazil",
    "South Africa",
    "Other",
  ];

  const citizenships = [
    "Indian",
    "American",
    "British",
    "Canadian",
    "Australian",
    "German",
    "French",
    "Japanese",
    "Chinese",
    "Brazilian",
    "South African",
    "Dual citizenship",
    "Other",
  ];

  const educationLevels = [
    "10th Class Passed",
    "12th Class Passed",
    "Graduate",
    "Post Graduate",
    "Doctorate",
    "Diploma",
    "Professional Degree",
    "Other",
  ];

  useEffect(() => {
    dispatch(setCurrentStep(3)); // Step 3: Additional Information
  }, [dispatch]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      nationality: "",
      religion: "",
      country_name: "",
      citizenship: "",
      education: "",
      occupation: "",
      organisation_name: "",
      designation_profession: "",
      net_worth: "",
      // businessNature: "",
    };

    if (!form.nationality) {
      newErrors.nationality = "nationality is required";
      isValid = false;
    }

    if (!form.religion) {
      newErrors.religion = "religion is required";
      isValid = false;
    }

    if (!form.country_name) {
      newErrors.country_name = "country_name is required";
      isValid = false;
    }

    if (!form.citizenship) {
      newErrors.citizenship = "citizenship is required";
      isValid = false;
    }

    if (!form.education) {
      newErrors.education = "education is required";
      isValid = false;
    }

    if (!form.occupation) {
      newErrors.occupation = "occupation is required";
      isValid = false;
    }

    if (!form.organisation_name) {
      newErrors.organisation_name = "organisation_name name is required";
      isValid = false;
    }

    if (!form.designation_profession) {
      newErrors.designation_profession = "designation_profession is required";
      isValid = false;
    }

    if (!form.net_worth) {
      newErrors.net_worth = "Net worth is required";
      isValid = false;
    }

    // if (!form.businessNature) {
    //   newErrors.businessNature = "Nature of business is required";
    //   isValid = false;
    // }

    setErrors(newErrors);
    return isValid;
  };

  const handleBack = () => {
    navigate("/kyc/personal");
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Validate that we have personal information
    if (!personalInfo.name) {
      toast.error("Please complete personal information first");
      navigate("/kyc/personal");
      return;
    }

    try {
      // Log the combined payload before sending
      console.log("Personal Info:", personalInfo);
      console.log("Additional Info:", form);

      // Call API to submit both personal and additional info with correct parameters
      const response = await kycService.submitPersonalInformation(
        personalInfo,
        form
      );

      if (response.success) {
        dispatch(updateAdditionalInfo(form));
        toast.success("KYC information submitted successfully");
        navigate("/kyc/address");
      } else {
        toast.error(response.message || "Failed to submit KYC information");
      }
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to submit KYC information"
      );
    }
  };

  return (
    <KYCLayout
      title="Additional Information"
      subtitle="A few more details to complete your profile."
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Row 1: nationality and religion */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Select
              value={form.nationality}
              onValueChange={(value) =>
                setForm((prev) => ({ ...prev, nationality: value }))
              }
            >
              <SelectTrigger
                className={`w-full h-12 bg-gray-50 border-gray-200 text-gray-500 ${
                  errors.nationality ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="nationality" />
              </SelectTrigger>
              <SelectContent>
                {nationalities.map((nationality) => (
                  <SelectItem key={nationality} value={nationality}>
                    {nationality}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.nationality && (
              <p className="mt-1 text-xs text-red-500">{errors.nationality}</p>
            )}
          </div>

          <div>
            <Select
              value={form.religion}
              onValueChange={(value) =>
                setForm((prev) => ({ ...prev, religion: value }))
              }
            >
              <SelectTrigger
                className={`w-full h-12 bg-gray-50 border-gray-200 text-gray-500 ${
                  errors.religion ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="religion" />
              </SelectTrigger>
              <SelectContent>
                {religions.map((religion) => (
                  <SelectItem key={religion} value={religion}>
                    {religion}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.religion && (
              <p className="mt-1 text-xs text-red-500">{errors.religion}</p>
            )}
          </div>
        </div>

        {/* Row 2: country_name and citizenship */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Select
              value={form.country_name}
              onValueChange={(value) =>
                setForm((prev) => ({ ...prev, country_name: value }))
              }
            >
              <SelectTrigger
                className={`w-full h-12 bg-gray-50 border-gray-200 text-gray-500 ${
                  errors.country_name ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="country_name name" />
              </SelectTrigger>
              <SelectContent>
                {countries.map((country_name) => (
                  <SelectItem key={country_name} value={country_name}>
                    {country_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.country_name && (
              <p className="mt-1 text-xs text-red-500">{errors.country_name}</p>
            )}
          </div>

          <div>
            <Select
              value={form.citizenship}
              onValueChange={(value) =>
                setForm((prev) => ({ ...prev, citizenship: value }))
              }
            >
              <SelectTrigger
                className={`w-full h-12 bg-gray-50 border-gray-200 text-gray-500 ${
                  errors.citizenship ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="citizenship" />
              </SelectTrigger>
              <SelectContent>
                {citizenships.map((citizenship) => (
                  <SelectItem key={citizenship} value={citizenship}>
                    {citizenship}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.citizenship && (
              <p className="mt-1 text-xs text-red-500">{errors.citizenship}</p>
            )}
          </div>
        </div>

        {/* Row 3: Person with Disability and education */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-3 h-12 px-3 bg-gray-50 border border-gray-200 rounded-md">
            <Checkbox
              id="disability"
              checked={form.persion_with_disability}
              onCheckedChange={(checked) =>
                setForm((prev) => ({
                  ...prev,
                  persion_with_disability: !!checked,
                }))
              }
              className="border-gray-400"
            />
            <label
              htmlFor="disability"
              className="text-gray-500 cursor-pointer"
            >
              Person with Disability
            </label>
          </div>

          <div>
            <Select
              value={form.education}
              onValueChange={(value) =>
                setForm((prev) => ({ ...prev, education: value }))
              }
            >
              <SelectTrigger
                className={`w-full h-12 bg-gray-50 border-gray-200 text-gray-500 ${
                  errors.education ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="education" />
              </SelectTrigger>
              <SelectContent>
                {educationLevels.map((education) => (
                  <SelectItem key={education} value={education}>
                    {education}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.education && (
              <p className="mt-1 text-xs text-red-500">{errors.education}</p>
            )}
          </div>
        </div>

        {/* Row 4: occupation and organisation_name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Input
              type="text"
              placeholder="occupation"
              value={form.occupation}
              onChange={(e) =>
                setForm((prev) => ({ ...prev, occupation: e.target.value }))
              }
              className={`w-full h-12 bg-gray-50 border-gray-200 placeholder:text-gray-500 ${
                errors.occupation ? "border-red-500" : ""
              }`}
            />
            {errors.occupation && (
              <p className="mt-1 text-xs text-red-500">{errors.occupation}</p>
            )}
          </div>

          <div>
            <Input
              type="text"
              placeholder="organisation_name name"
              value={form.organisation_name}
              onChange={(e) =>
                setForm((prev) => ({
                  ...prev,
                  organisation_name: e.target.value,
                }))
              }
              className={`w-full h-12 bg-gray-50 border-gray-200 placeholder:text-gray-500 ${
                errors.organisation_name ? "border-red-500" : ""
              }`}
            />
            {errors.organisation_name && (
              <p className="mt-1 text-xs text-red-500">
                {errors.organisation_name}
              </p>
            )}
          </div>
        </div>

        {/* Row 5: designation_profession */}
        <div>
          <Input
            type="text"
            placeholder="designation_profession"
            value={form.designation_profession}
            onChange={(e) =>
              setForm((prev) => ({
                ...prev,
                designation_profession: e.target.value,
              }))
            }
            className={`w-full h-12 bg-gray-50 border-gray-200 placeholder:text-gray-500 ${
              errors.designation_profession ? "border-red-500" : ""
            }`}
          />
          {errors.designation_profession && (
            <p className="mt-1 text-xs text-red-500">
              {errors.designation_profession}
            </p>
          )}
        </div>

        {/* Row 6: Net Worth and Nature of Business */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Input
              type="text"
              placeholder="Net worth"
              value={form.net_worth}
              onChange={(e) =>
                setForm((prev) => ({ ...prev, net_worth: e.target.value }))
              }
              className={`w-full h-12 bg-gray-50 border-gray-200 placeholder:text-gray-500 ${
                errors.net_worth ? "border-red-500" : ""
              }`}
            />
            {errors.net_worth && (
              <p className="mt-1 text-xs text-red-500">{errors.net_worth}</p>
            )}
          </div>

          {/* <div>
            <Input
              type="text"
              placeholder="Nature of business"
              value={form.businessNature}
              onChange={(e) =>
                setForm((prev) => ({
                  ...prev,
                  businessNature: e.target.value,
                }))
              }
              className={`w-full h-12 bg-gray-50 border-gray-200 placeholder:text-gray-500 ${
                errors.businessNature ? "border-red-500" : ""
              }`}
            />
            {errors.businessNature && (
              <p className="mt-1 text-xs text-red-500">
                {errors.businessNature}
              </p>
            )}
          </div> */}
        </div>

        {/* Error Display */}
        {(submissionError || apiErrors.personalInfo) && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 text-sm">
              {submissionError || apiErrors.personalInfo}
            </p>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            onClick={handleBack}
            variant="outline"
            className="border-gray-300 px-8 py-2 h-12 rounded-md"
          >
            Back
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || apiLoading.personalInfo}
            className="bg-[#26355E] hover:bg-[#26355E]/90 text-white px-8 py-2 h-12 rounded-md disabled:opacity-50"
          >
            {isSubmitting || apiLoading.personalInfo
              ? "Submitting..."
              : "Proceed"}
          </Button>
        </div>
      </form>
    </KYCLayout>
  );
};

export default AdditionalInfo;
